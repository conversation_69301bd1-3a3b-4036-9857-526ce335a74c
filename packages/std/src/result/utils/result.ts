/** biome-ignore-all lint/nursery/useExportsLast: Redundant */

import type { BlobType } from '../../shared'

export const okSym = Symbol('ok')
export const errSym = Symbol('error')

export const ok = <Type>(value: Type): Type extends std.Ok<BlobType> ? Type : std.Ok<Type> => {
  if (value && typeof value === 'object' && okSym in value) {
    return value as BlobType
  }

  return {
    [okSym]: value,
    [Symbol.for('nodejs.util.inspect.custom')]: () => value,

    toJSON: () => {
      return value
    },
    toString: () => {
      return value
    },
    *[Symbol.iterator]() {
      yield this.value
    },
  } as BlobType
}

export const isOk = <Type>(value: Type): value is Type => {
  return value && typeof value === 'object' && okSym in value
}

export const err = <Name extends string, Causes extends string[] = []>(
  name: Name,
  message: string,
  ...causes: Causes
): std.Err<Name, Causes> => {
  const log = `Err<'${name}', '${message}', [${causes.map(cause => `'${cause}'`).join(', ')}]>`

  return {
    [errSym]: [name, message, causes],
    [Symbol.for('nodejs.util.inspect.custom')]: () => {
      return log
    },

    *[Symbol.iterator]() {
      // biome-ignore lint/complexity/noUselessThisAlias: Redundant
      const self = this
      yield self
      return self
    },
  }
}

export const isErr = <Type>(value: Type): value is Type => {
  return value && typeof value === 'object' && errSym in value
}
